import {
  IsOptional,
  IsString,
  Is<PERSON>num,
  IsUUI<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>rra<PERSON>,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  DocumentStatus,
  DocumentRepositoryType,
} from '../entities/document.entity';

export enum SortField {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  FILE_NAME = 'fileName',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class SearchDocumentsDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(DocumentStatus)
  f_status?: DocumentStatus;

  @IsOptional()
  @IsEnum(DocumentRepositoryType)
  f_repositoryType?: DocumentRepositoryType;

  @IsOptional()
  @IsUUID('4')
  f_assignmentGroupId?: string;

  @IsOptional()
  @IsUUID('4')
  f_courseId?: string;

  @IsOptional()
  @IsUUID('4')
  f_uploaderId?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map((tag) => tag.trim());
    }
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return value;
  })
  f_tags?: string[];

  @IsOptional()
  @IsEnum(SortField)
  sortBy?: SortField = SortField.CREATED_AT;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  offset?: number = 0;

  // TODO: for testing, need remove
  @IsOptional()
  @IsUUID('4')
  userId?: string;
}
